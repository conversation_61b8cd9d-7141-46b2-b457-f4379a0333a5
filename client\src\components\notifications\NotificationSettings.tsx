import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>, <PERSON>Off, Settings } from 'lucide-react';
import { pushNotificationManager } from '@/utils/pushNotifications';

interface NotificationSettingsProps {
  onSubscriptionChange?: (subscriptionData: any) => void;
}

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({ 
  onSubscriptionChange 
}) => {
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeNotifications();
  }, []);

  const initializeNotifications = async () => {
    try {
      const initialized = await pushNotificationManager.initialize();
      setIsInitialized(initialized);
      
      if (initialized) {
        setPermission(pushNotificationManager.getPermissionStatus());
        setIsSubscribed(pushNotificationManager.isSubscribed());
      }
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
    }
  };

  const handleEnableNotifications = async () => {
    setIsLoading(true);
    try {
      const subscriptionData = await pushNotificationManager.subscribe();
      if (subscriptionData) {
        setIsSubscribed(true);
        setPermission('granted');
        onSubscriptionChange?.(subscriptionData);
        
        // Show a test notification
        pushNotificationManager.showLocalNotification(
          'Notifications Enabled!',
          'You will now receive push notifications for new messages.'
        );
      }
    } catch (error) {
      console.error('Failed to enable notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisableNotifications = async () => {
    setIsLoading(true);
    try {
      const success = await pushNotificationManager.unsubscribe();
      if (success) {
        setIsSubscribed(false);
        onSubscriptionChange?.(null);
      }
    } catch (error) {
      console.error('Failed to disable notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusText = () => {
    if (!isInitialized) {
      return 'Push notifications are not supported in this browser.';
    }
    
    switch (permission) {
      case 'granted':
        return isSubscribed 
          ? 'Push notifications are enabled. You will receive notifications for new messages.'
          : 'Push notifications are allowed but not active.';
      case 'denied':
        return 'Push notifications are blocked. Please enable them in your browser settings.';
      default:
        return 'Push notifications are not enabled. Click the button below to enable them.';
    }
  };

  const getStatusColor = () => {
    if (!isInitialized) return 'text-muted-foreground';
    
    switch (permission) {
      case 'granted':
        return isSubscribed ? 'text-green-600' : 'text-yellow-600';
      case 'denied':
        return 'text-red-600';
      default:
        return 'text-muted-foreground';
    }
  };

  if (!isInitialized) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BellOff className="h-5 w-5" />
            Notifications Not Supported
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Push notifications are not supported in this browser.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Notification Settings
        </CardTitle>
        <CardDescription>
          Manage your push notification preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          {isSubscribed ? (
            <Bell className="h-5 w-5 text-green-600" />
          ) : (
            <BellOff className="h-5 w-5 text-muted-foreground" />
          )}
          <div className="flex-1">
            <p className={`text-sm ${getStatusColor()}`}>
              {getStatusText()}
            </p>
          </div>
        </div>
        
        <div className="flex gap-2">
          {!isSubscribed ? (
            <Button 
              onClick={handleEnableNotifications}
              disabled={isLoading || permission === 'denied'}
              className="flex-1"
            >
              {isLoading ? 'Enabling...' : 'Enable Notifications'}
            </Button>
          ) : (
            <Button 
              onClick={handleDisableNotifications}
              disabled={isLoading}
              variant="outline"
              className="flex-1"
            >
              {isLoading ? 'Disabling...' : 'Disable Notifications'}
            </Button>
          )}
        </div>
        
        {permission === 'denied' && (
          <p className="text-xs text-muted-foreground">
            To enable notifications, please allow them in your browser settings and refresh the page.
          </p>
        )}
      </CardContent>
    </Card>
  );
};
