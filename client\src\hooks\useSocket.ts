import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';
import type { Message } from '@/components/chat/MessageList';
import type { User } from '@/components/chat/UserList';

interface UseSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  messages: Message[];
  users: User[];
  typingUsers: string[];
  joinChat: (username: string) => void;
  sendMessage: (message: string) => void;
  sendTyping: (isTyping: boolean) => void;
  currentUserId: string | null;
  subscribeToPush: (subscriptionData: any) => void;
}

export const useSocket = (serverUrl: string = 'http://localhost:3001'): UseSocketReturn => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const currentUserRef = useRef<string>('');

  useEffect(() => {
    const newSocket = io(serverUrl, {
      autoConnect: false,
    });

    newSocket.on('connect', () => {
      setIsConnected(true);
      setCurrentUserId(newSocket.id);
      console.log('Connected to server');
    });

    newSocket.on('disconnect', () => {
      setIsConnected(false);
      setCurrentUserId(null);
      console.log('Disconnected from server');
    });

    newSocket.on('newMessage', (message: Message) => {
      setMessages(prev => [...prev, message]);

      // Show toast notification for messages from other users
      if (message.userId !== newSocket.id && message.username !== 'System') {
        toast.success(`New message from ${message.username}`, {
          description: message.text,
          duration: 4000,
        });
      }
    });

    newSocket.on('onlineUsers', (userList: User[]) => {
      setUsers(userList);
    });

    newSocket.on('updateUserList', (userList: User[]) => {
      setUsers(userList);
    });

    newSocket.on('userJoined', ({ username }: { username: string; userId: string }) => {
      setMessages(prev => [...prev, {
        id: Date.now(),
        username: 'System',
        text: `${username} joined the chat`,
        timestamp: new Date().toISOString(),
        userId: 'system'
      }]);

      // Show toast notification for user joining
      if (username !== currentUserRef.current) {
        toast.info(`${username} joined the chat`, {
          duration: 3000,
        });
      }
    });

    newSocket.on('userLeft', ({ username }: { username: string; userId: string }) => {
      setMessages(prev => [...prev, {
        id: Date.now(),
        username: 'System',
        text: `${username} left the chat`,
        timestamp: new Date().toISOString(),
        userId: 'system'
      }]);

      // Show toast notification for user leaving
      toast.warning(`${username} left the chat`, {
        duration: 3000,
      });
    });

    newSocket.on('userTyping', ({ username, isTyping }: { username: string; isTyping: boolean }) => {
      setTypingUsers(prev => {
        if (isTyping) {
          return prev.includes(username) ? prev : [...prev, username];
        } else {
          return prev.filter(user => user !== username);
        }
      });
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [serverUrl]);

  const joinChat = (username: string) => {
    if (socket && !isConnected) {
      currentUserRef.current = username;
      socket.connect();
      socket.emit('join', { username });
    }
  };

  const sendMessage = (message: string) => {
    if (socket && isConnected) {
      socket.emit('sendMessage', { text: message });
    }
  };

  const sendTyping = (isTyping: boolean) => {
    if (socket && isConnected) {
      socket.emit('typing', { isTyping });
    }
  };

  const subscribeToPush = (subscriptionData: any) => {
    if (socket && isConnected && subscriptionData) {
      socket.emit('subscribe', subscriptionData);
    }
  };

  return {
    socket,
    isConnected,
    messages,
    users,
    typingUsers,
    joinChat,
    sendMessage,
    sendTyping,
    currentUserId,
    subscribeToPush
  };
};
