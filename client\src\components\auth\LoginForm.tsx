import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MessageCircle } from 'lucide-react';

interface LoginFormProps {
  onLogin: (username: string) => void;
  isConnecting?: boolean;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onLogin, isConnecting = false }) => {
  const [username, setUsername] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (username.trim() && !isConnecting) {
      onLogin(username.trim());
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <MessageCircle className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-2xl">Join Chat Room</CardTitle>
          <CardDescription>
            Enter your username to start chatting with others
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                type="text"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={isConnecting}
                maxLength={20}
                autoFocus
              />
            </div>
            <Button 
              type="submit" 
              className="w-full" 
              disabled={!username.trim() || isConnecting}
            >
              {isConnecting ? 'Connecting...' : 'Join Chat'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
