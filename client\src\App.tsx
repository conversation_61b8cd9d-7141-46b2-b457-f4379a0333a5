import React, { useState } from 'react';
import { ChatLayout } from './components/chat/ChatLayout';
import { LoginForm } from './components/auth/LoginForm';
import { NotificationSettings } from './components/notifications/NotificationSettings';
import { useSocket } from './hooks/useSocket';
import { Button } from './components/ui/button';
import { Settings } from 'lucide-react';
import { Toaster } from 'sonner';

function App() {
  const [username, setUsername] = useState<string>('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);

  const {
    isConnected,
    messages,
    users,
    typingUsers,
    joinChat,
    sendMessage,
    sendTyping,
    currentUserId,
    subscribeToPush
  } = useSocket();

  const handleLogin = (enteredUsername: string) => {
    setUsername(enteredUsername);
    setIsLoggedIn(true);
    joinChat(enteredUsername);
  };

  const handleNotificationSubscription = (subscriptionData: any) => {
    subscribeToPush(subscriptionData);
  };

  if (!isLoggedIn) {
    return <LoginForm onLogin={handleLogin} isConnecting={false} />;
  }

  if (showNotificationSettings) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <div className="space-y-4">
          <NotificationSettings onSubscriptionChange={handleNotificationSubscription} />
          <Button
            onClick={() => setShowNotificationSettings(false)}
            variant="outline"
            className="w-full"
          >
            Back to Chat
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <ChatLayout
        messages={messages}
        users={users}
        currentUserId={currentUserId}
        typingUsers={typingUsers}
        onSendMessage={sendMessage}
        onTyping={sendTyping}
        isConnected={isConnected}
      />

      {/* Floating notification settings button */}
      <Button
        onClick={() => setShowNotificationSettings(true)}
        size="icon"
        className="fixed bottom-4 right-4 rounded-full shadow-lg"
        title="Notification Settings"
      >
        <Settings className="h-4 w-4" />
      </Button>

      {/* Sonner Toaster for notifications */}
      <Toaster position="top-right" richColors />
    </div>
  );
}

export default App;
